source 'https://github.com/CocoaPods/Specs.git'
source 'https://cdn.cocoapods.org/'

require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '12.0'
source 'https://github.com/CocoaPods/Specs.git'
install! 'cocoapods', :deterministic_uuids => false

target 'StepAll' do
  config = use_native_modules!
  pod 'GoogleMaps'
  pod 'Firebase/Core', :modular_headers => true
  pod 'Firebase/Messaging', :modular_headers => true
  pod 'Google-Maps-iOS-Utils'
  pod 'react-native-image-picker', :path => '../node_modules/react-native-image-picker'
  pod 'RNI18n', :path => '../node_modules/react-native-i18n'
  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'RNSound', :path => '../node_modules/react-native-sound'
  pod 'AppsFlyerFramework'

  $RNFirebaseAsStaticFramework = true
  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => flags[:hermes_enabled],
    :fabric_enabled => flags[:fabric_enabled],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # pod 'RNAudioRecorderPlayer', :path => '../node_modules/react-native-audio-recorder-player'

  pod 'rn-fetch-blob', :path => '../node_modules/rn-fetch-blob'

  pod 'RNSound', :path => '../node_modules/react-native-sound'

  pod 'RNExitApp', :path => '../node_modules/react-native-exit-app'

  pod 'RNBackgroundFetch', :path => '../node_modules/react-native-background-fetch'


  target 'StepAllTests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  use_flipper!()

  post_install do |installer|
    react_native_post_install(installer)
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
    installer.pods_project.build_configurations.each do |config|
    config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
  end
end
end


