<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.StepAll">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <!-- <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" /> -->
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.VIBRATE" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  <uses-permission android:name="android.permission.RECORD_AUDIO" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
  <uses-permission android:name="android.permission.WAKE_LOCK" />
  <!-- <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" /> -->

  <!-- android:roundIcon="@mipmap/ic_launcher_round" -->
  <application
    android:name=".MainApplication"
    android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher_round"
    android:allowBackup="false"
    android:theme="@style/AppTheme"
    android:usesCleartextTraffic="true"
  >
  <service
    android:name=".ForegroundServiceModule"
    android:foregroundServiceType="location|dataSync"
    android:exported="true"
    />
    <receiver android:name=".WakeUpReceiver" android:exported="true" />
    <!-- <service android:name=".ForegroundService" android:exported="false" /> -->
    <!-- android:foregroundServiceType="location" -->
    <!-- android:foregroundServiceType="location|connectedDevice|mediaPlayback|dataSync" -->

    <!-- <service android:name=".AppForegroundService"/> -->
   <activity
            android:name=".NativeUI"
            android:exported="false" />
    <meta-data
      android:name="com.google.android.geo.API_KEY"
      android:value="AIzaSyAoPPiK1g9uWo8nKg1-54ejX5_LMmdsncw" />
    <!-- <meta-data
        android:name="com.appsflyer.AF_DEV_KEY"
        android:value="LJxrXqHE82GrmWngVM7nSQ"/> -->
    <meta-data android:name="com.facebook.sdk.ApplicationId"
      android:value="@string/facebook_app_id" />
    <activity
      android:name=".MainActivity"
      android:label="@string/app_name"
      android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
      android:launchMode="singleTask"
      android:screenOrientation="portrait"
      android:windowSoftInputMode="adjustPan"
      android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
       <!-- <intent-filter android:label="Step" android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                        <data android:scheme="stepAll" android:host="open" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="step-db.com" />
            </intent-filter> -->
    </activity>
  </application>
</manifest>