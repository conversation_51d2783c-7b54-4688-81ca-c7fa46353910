import React, { Component, useEffect, useRef, useState } from 'react';
import {
    View,
    Text,
    Image,
    ImageBackground,
    StyleSheet,
    I18nManager,
    TouchableOpacity,
} from 'react-native';
import {
    appFont,
    appFontBold,
    Green,
    WhiteGreen,
    screenWidth,
    White,
    DarkGreen,
    Blue,
    MediumGrey,
    DarkGrey,
    WhiteGery,
    Black,
    screenHeight,
    Red,
    MediumGreen,
    appColor1,
    Grey,
    DarkYellow,
    DarkBlue,
    LightGreen,
    MediumYellow,
} from '../components/Styles';
import { Button, DatePicker, Textarea } from 'native-base';
import Header from '../components/Header';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Footer from '../components/Footer';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as packagesActions from '../../Store/Actions/packages';
import RBSheet from 'react-native-raw-bottom-sheet';
import Loading from '../components/Loading';



const MyPackageDetails = props => {
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [days, setDays] = useState([]);
    const [destinations, setDestinations] = useState([]);
    const [detailsPackage, setDetailsPackage] = useState({});
    const [offers, setOffers] = useState([]);
    const refRBSheetReasons = useRef();
    const [reasonText, setReasonText] = useState('');


    console.log(I18nManager.isRTL);

    useEffect(() => {
        console.log('item', props.route.params.package);

        const getMyPackage = async () => {
            setLoading(true)
            try {
                let response = await dispatch(packagesActions.getMyPackage(props.route.params.package.id));
                if (response.success == true) {
                    setDays(response.data.repeat_days);
                    setDetailsPackage(response.data);
                    setOffers(response.data.offers);
                    setDestinations(response.data.destinations);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            }
            catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getMyPackage();
    }, []);

    const reportProblem = async () => {
        if (reasonText == '') {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Please_enter_reason_first'),
                White,
                1500,
                screenHeight / 50,
            );
        }
        else {
            setLoadingMore(true)
            try {
                let response = await dispatch(packagesActions.ReportProblem(props.route.params.package.id, reasonText));
                if (response.success == true) {
                    props.navigation.navigate('Home');
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoadingMore(false);
            }
            catch (err) {
                console.log('err', err)
                setLoadingMore(false);
            }
        }
    };


    if (loading) {

        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Notifications')}
                    drawerPress={() => {
                        props.navigation.navigate('More');
                    }}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

            </View>
        );

    } else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header
                    title={strings('lang.Mypackagedetails')}
                    backPress={() => {
                        props.navigation.goBack();
                    }}
                />

                {loadingMore
                    ?
                    <Loading />
                    :
                    <></>
                }

                <ScrollView
                    style={{ width: '100%', }}
                    showsVerticalScrollIndicator={false}
                >
                    <Text style={[styles.blackText, { fontSize: screenWidth / 30, alignSelf: 'flex-start', marginStart: '2.5%', marginBottom: '2.5%' }]}>{strings('lang.destination')}</Text>
                    {destinations.map((item, index) => {
                        return <>
                            <View
                                style={[styles.contentContainer, { width: '95%', justifyContent: 'flex-start', alignItems: 'center', marginVertical: 10, }]}>
                                <View style={{ height: screenHeight / 25, width: '8%', alignItems: 'center', justifyContent: 'flex-start' }}>
                                    <Image source={require('../images/bluelocation.png')} style={styles.locationImage} />
                                </View>
                                <View style={{ width: '92%' }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{item.from_address}</Text>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', alignSelf: 'flex-start' }}>{''}</Text>
                                </View>
                            </View>
                            <View
                                style={[styles.contentContainer, { width: '95%', justifyContent: 'flex-start', alignItems: 'center', marginTop: screenHeight / 100 }]}>
                                <View style={{ height: screenHeight / 25, width: '8%', alignItems: 'center', justifyContent: 'flex-start' }}>
                                    <Image source={require('../images/yallowlocation.png')} style={styles.locationImage} />
                                </View>
                                <View style={{ width: '92%' }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%', alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{item.to_address}</Text>
                                </View>
                            </View>
                            {item.type
                                ?
                                <>
                                    <View style={[styles.dataContainer, { borderBottomWidth: 0 }]}>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 30 }]}>{strings('lang.facetype')}</Text>
                                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginHorizontal: '7%', opacity: .8 }]}>{[item.type]}</Text>
                                    </View>
                                </>
                                :
                                <></>
                            }
                            <View style={{ width: '95%', height: .8, backgroundColor: MediumGrey, alignSelf: 'center' }}></View>
                        </>
                    })}

                    <View style={styles.dataContainer}>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 30 }]}>{strings('lang.message26')}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '6%', opacity: .8 }]}>{detailsPackage.start_date}</Text>
                    </View>

                    <View style={styles.dataContainer}>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 30 }]}>{strings('lang.message27')}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '6%', opacity: .8 }]}>{detailsPackage.end_date}</Text>
                    </View>

                    <View style={styles.dataContainer}>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 30 }]}>{strings('lang.timetomoveon')}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '6%', opacity: .8 }]}>{detailsPackage.start_time}</Text>
                    </View>


                    <View style={styles.dataContainer}>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 30 }]}>{strings('lang.returntime')}</Text>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 35, marginStart: '6%', opacity: .8 }]}>{detailsPackage.return_time}</Text>
                    </View>


                    <View style={[styles.dataContainer, { flexDirection: 'column', alignItems: 'flex-start', minHeight: screenHeight / 10, justifyContent: 'center', }]}>
                        <Text style={[styles.blackText, { fontSize: screenWidth / 30 }]}>{strings('lang.Repeatthenumberoftimesaweek')}</Text>
                        <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                            {days.map((item, index) => {
                                return <Text style={[styles.blackText, { fontSize: screenWidth / 30, }]}>{[strings(`lang.${item}`), ' . ']}</Text>
                            })}

                        </View>
                    </View>

                    {/* {detailsPackage.driver_package_trip && detailsPackage.driver_package_trip.driver
                        &&
                        <View style={{ width: '95%', flexDirection: 'row', justifyContent: 'flex-start', paddingVertical: screenHeight / 50, alignSelf: 'center' }}>
                            <View style={{ width: screenWidth / 8, height: screenWidth / 8, marginEnd: '2%' }}>
                                <Image source={detailsPackage.driver_package_trip.driver.image ? { uri: detailsPackage.driver_package_trip.driver.image } : require('../images/Group-26.png')} style={{ borderRadius: 100, height: '100%', resizeMode: 'contain', width: '100%' }} />
                            </View>
                            <View style={{ width: '60%', alignItems: 'flex-start', alignSelf: 'center' }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '1%', lineHeight: screenHeight / 40 }}>{detailsPackage.driver_package_trip.driver.name}</Text>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '1%', lineHeight: screenHeight / 40 }}>{detailsPackage.driver_package_trip.driver.vehicle.car_brand && detailsPackage.driver_package_trip.driver.vehicle.car_brand.name} - {detailsPackage.driver_package_trip.driver.vehicle.car_model && detailsPackage.driver_package_trip.driver.vehicle.car_model.name} - {detailsPackage.driver_package_trip.driver.vehicle.year && detailsPackage.driver_package_trip.driver.vehicle.year}</Text>
                                    <View style={{ width: screenWidth / 22, height: screenWidth / 22, borderRadius: screenWidth / 24, borderWidth: .8, borderColor: MediumGrey, backgroundColor: detailsPackage.driver_package_trip.driver.vehicle.color && detailsPackage.driver_package_trip.driver.vehicle.color, marginStart: 5 }}></View>
                                </View>
                                <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 30, alignItems: 'center', justifyContent: 'space-between' }}>
                                    <View style={{ flexDirection: 'row', width: '80%', height: screenHeight / 30, alignItems: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{detailsPackage.driver_package_trip.driver.vehicle.plate_number + ' ' + detailsPackage.driver_package_trip.driver.vehicle.plate_letter_right + ' ' + detailsPackage.driver_package_trip.driver.vehicle.plate_letter_middle + ' ' + detailsPackage.driver_package_trip.driver.vehicle.plate_letter_left}</Text>
                                        <View style={{ height: '80%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey, marginHorizontal: 10 }}></View>
                                        <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '5%', marginEnd: 5, alignSelf: 'center' }} />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {detailsPackage.driver_package_trip.driver.avg_rating}</Text>
                                    </View>
                                </View>
                            </View>
                            {detailsPackage.status != 'finished' && detailsPackage.status != "cancelled"
                                &&
                                <View style={{ width: '20%', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Button transparent
                                        onPress={() => { Linking.openURL(`tel:${'+966'}${detailsPackage.driver_package_trip.driver.mobile_number}`) }}
                                        style={{ backgroundColor: WhiteGreen, width: '100%', flexDirection: 'row', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                        <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.Call')}</Text>
                                    </Button>

                                    <Button transparent
                                        onPress={() => { Linking.openURL(`whatsapp://send?text=&phone=${'+966'}${detailsPackage.driver_package_trip.driver.formatted_phone}`) }}
                                        style={{ backgroundColor: White, width: '100%', flexDirection: 'row', borderWidth: 1, borderColor: MediumGrey, marginTop: '5%', height: screenHeight / 30, borderRadius: 20, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                        <Image source={require('../images/whatssapp.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 55, color: Black, marginHorizontal: 2 }}>{strings('lang.whatssapp')}</Text>
                                    </Button>
                                </View>
                            }
                        </View>
                    } */}


                    {/* <Text style={[styles.blackText, { fontSize: screenWidth / 30, alignSelf: 'flex-start', marginStart: '2.5%', marginTop: '2.5%' }]}>{strings('lang.Driverandvehicledetails')}</Text> */}
                    {/* <View style={{ width: '100%', flexDirection: 'row', justifyContent: 'center', paddingTop: screenHeight / 100 }}>
                        <View style={{ width: '20%', height: screenHeight / 15 }}>
                            <Image source={require('../images/profile.png')} style={{ height: '100%', resizeMode: 'contain', width: '90%' }} />
                        </View>

                        <View style={{ width: '80%', alignItems: 'flex-start', }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, marginStart: '1%', lineHeight: screenHeight / 30 }}>{'Chevorlet Corvette'}</Text>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: MediumGrey, marginStart: '1%', lineHeight: screenHeight / 40 }}>{'مجدى السيد'}</Text>
                            <View style={{ width: '100%', flexDirection: 'row', height: screenHeight / 25, alignItems: 'center' }}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginHorizontal: '2%' }}>{'5520 AA'}</Text>
                                <View style={{ height: '70%', alignSelf: 'center', width: 1, backgroundColor: MediumGrey }}></View>
                                <Image source={require('../images/star.png')} style={{ resizeMode: 'contain', width: '10%', alignSelf: 'center' }} />
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, marginStart: '1%' }}> {'4.5'}</Text>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 20, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around', }}>
                                <Button transparent style={{ backgroundColor: LightGreen, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    <Image source={require('../images/call.png')} style={{ resizeMode: 'contain', width: '10%', marginHorizontal: '2%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, }}>{strings('lang.Call')}</Text>
                                </Button>
                                <Button transparent style={{ backgroundColor: White, borderWidth: 1, borderColor: MediumGrey, width: '40%', flexDirection: 'row', marginEnd: '2%', height: screenHeight / 25, borderRadius: 30, alignItems: 'center', justifyContent: 'center', alignSelf: 'center' }}>
                                    <Image source={require('../images/whatssapp.png')} style={{ resizeMode: 'contain', width: '15%', marginHorizontal: '2%' }} />
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, }}>{strings('lang.whatssapp')}</Text>
                                </Button>
                            </View>
                        </View>
                    </View> */}


                    {detailsPackage.package
                        &&
                        <>
                            <View style={{ height: screenHeight / 10 }}></View>

                            <Button onPress={() => { props.navigation.navigate('ReportaVacation', { item: detailsPackage }) }} transparent
                                style={{
                                    width: '90%', height: screenHeight / 20,
                                    alignSelf: 'center', justifyContent: 'center', alignItems: 'center',
                                    backgroundColor: DarkBlue, borderRadius: 20,
                                }}>
                                <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 32, }}>{strings('lang.Reportavacation').toUpperCase()}</Text>
                            </Button>

                            <Button onPress={() => { refRBSheetReasons.current.open(); }} transparent
                                style={{
                                    width: '90%', height: screenHeight / 20,
                                    alignSelf: 'center', justifyContent: 'center', alignItems: 'center',
                                    backgroundColor: WhiteGery, borderRadius: 20, marginVertical: '2%'
                                }}>
                                <Text style={{ fontFamily: appFontBold, color: Black, fontSize: screenWidth / 32, }}>{strings('lang.Reportaproblem').toUpperCase()}</Text>
                            </Button>
                        </>
                    }

                    {!detailsPackage.package && offers.length != 0
                        ?
                        offers.map((item) => {
                            return (
                                <View style={{ width: '95%', alignItems: 'center', justifyContent: 'center', alignSelf: 'center', marginVertical: item.best_offer ? screenHeight / 50 : screenHeight / 80, borderWidth: 1, borderColor: item.tag ? MediumYellow : Grey, borderRadius: 20, paddingVertical: screenHeight / 60 }}>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '90%', alignSelf: 'center', marginBottom: '2%' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 22, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}> {item.package && item.package.name} </Text>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 22, color: DarkYellow, textAlign: I18nManager.isRTL ? 'left' : 'right', }}>{item.final_price} {strings('lang.SR')} </Text>
                                    </View>
                                    {item.discount_amount
                                        ?
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '90%', alignSelf: 'center', marginBottom: '2%', backgroundColor: WhiteGery, padding: 5 }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Red, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{strings('lang.Discount')} {item.discount_amount} {strings('lang.SR')} {strings('lang.limited')}</Text>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: DarkGrey, textAlign: I18nManager.isRTL ? 'left' : 'right', textDecorationLine: 'line-through', }}>{item.price} {strings('lang.SR')}</Text>
                                        </View>
                                        :
                                        <></>
                                    }

                                    <TouchableOpacity
                                        onPress={() => { props.navigation.navigate('PaymentMethods', { subscription: true, item: item }) }}
                                        style={{ height: screenHeight / 25, width: '90%', alignItems: 'center', justifyContent: 'center', backgroundColor: DarkBlue, borderRadius: 20 }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: White, }}> {strings('lang.Confirm')} </Text>
                                    </TouchableOpacity>

                                    {item.best_offer
                                        ?
                                        <View style={{ width: screenWidth / 2, height: screenHeight / 30, backgroundColor: MediumYellow, position: 'absolute', top: -screenHeight / 55, alignItems: 'center', justifyContent: 'center', borderRadius: 5 }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: White, }}>{strings('lang.bestValue')}</Text>
                                        </View>
                                        :
                                        <></>
                                    }

                                </View>
                            )
                        })
                        :
                        !detailsPackage.payment_method_id &&
                        <Text style={{ fontFamily: appFontBold, color: Red, fontSize: screenWidth / 24, textAlign: 'center', marginTop: screenHeight / 80 }}>{strings('lang.ThereIsNoOffersYet')}</Text>
                    }

                    <View style={{ height: screenHeight / 15 }}></View>


                </ScrollView>




                <RBSheet
                    ref={refRBSheetReasons}
                    height={screenHeight / 3}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            width: '100%',

                            alignSelf: 'center',
                            borderTopStartRadius: 15,
                            borderTopEndRadius: 15

                        },
                        wrapper: {

                        },
                        draggableIcon: {

                        }
                    }}
                // onClose={() => { props.navigation.navigate('Home') }}
                // closeOnDragDown={true}
                >
                    <ScrollView
                        style={{ alignSelf: 'center', width: '100%', paddingVertical: screenHeight / 50 }}
                        showsVerticalScrollIndicator={false}
                    >
                        {/* <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'flex-start', width: '100%', paddingVertical: '3%', }}> */}
                        {/* <Image source={require('../images/modrek/animal.png')} style={{ resizeMode: 'contain', width: '60%', height: '30%', alignItems: 'center', tintColor: Red, marginTop: '20%', marginBottom: '10%' }} /> */}
                        <View style={{ height: screenHeight / 25, width: '60%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', }}>
                            <Text style={{ fontSize: screenWidth / 25, fontFamily: appFontBold, color: DarkGrey, }}>{strings('lang.whatIsTheProblem')}</Text>
                        </View>

                        <View style={{ width: '95%', height: screenHeight / 7, alignSelf: 'center', marginVertical: '2.5%' }}>
                            {/* <Text style={{ fontSize: screenWidth / 26, fontFamily: appFont, color: Black, alignSelf: 'flex-start' }}>{strings('lang.Pleasewritethereason')}</Text> */}
                            <Textarea
                                placeholder={strings('lang.whatIsTheProblem')}
                                onChangeText={text => setReasonText(text)}
                                value={reasonText}
                                style={{
                                    width: '100%',
                                    alignSelf: 'center',
                                    borderRadius: 10,
                                    paddingHorizontal: '5%',
                                    borderColor: MediumGrey,
                                    color: DarkGrey,
                                    borderWidth: 1,
                                    fontFamily: appFontBold,
                                    height: '100%',
                                    textAlignVertical: 'top',
                                    fontSize: screenWidth / 30,
                                    textAlign: I18nManager.isRTL ? 'right' : 'left',
                                }}
                            />
                        </View>
                        {/* </View> */}
                    </ScrollView>
                    <View style={{ alignItems: "center", justifyContent: 'space-between', width: '95%', alignSelf: "center", height: screenHeight / 20, flexDirection: 'row', }}>
                        <Button onPress={() => {
                            refRBSheetReasons.current.close(); reportProblem()
                        }} style={{ width: '45%', alignSelf: "center", height: '100%', backgroundColor: DarkBlue, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Confirm')}</Text>
                        </Button>
                        <Button onPress={() => { refRBSheetReasons.current.close(); }}
                            style={{ width: '45%', alignSelf: "center", height: '100%', backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.back')}</Text>
                        </Button>
                    </View>
                    <View style={{ height: screenHeight / 20 }}></View>

                </RBSheet>

            </View>
        );
    }
};

const styles = StyleSheet.create({
    container: {
        height: screenHeight / 9,
        width: '95%',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
        borderBottomWidth: .8,
        marginTop: '3%',
        borderBottomColor: MediumGrey
    },
    imageContainer: {
        height: '65%',
        width: '10%',
        alignSelf: 'flex-start',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    image: {
        height: '25%',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
    locationImage: {
        height: '100%',
        width: '90%',
        resizeMode: 'contain'
    },
    addressContainer: {
        height: '100%',
        width: '90%',
        alignSelf: 'flex-start',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
    },
    blackText: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        textAlign: I18nManager.isRTL ? 'left' : 'right'
    },
    dataContainer: {
        height: screenHeight / 18,
        width: '95%',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
        alignSelf: 'center',
        borderBottomColor: MediumGrey,
        borderBottomWidth: .8
    },
    contentContainer: {
        width: '90%',
        alignSelf: 'center',
        justifyContent: 'center',
        marginVertical: '2%',
        height: screenHeight / 18,
        borderRadius: 5,
        paddingHorizontal: 0,
        flexDirection: 'row',
    },
    locationImage: {
        resizeMode: 'contain',
        width: '50%',
        height: '50%'
    },
});

export default MyPackageDetails;
