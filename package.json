{"name": "StepAll", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.2.2", "@miblanchard/react-native-slider": "^2.1.0", "@react-native-async-storage/async-storage": "^1.17.7", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/datetimepicker": "^3.0.2", "@react-native-community/netinfo": "^9.3.0", "@react-native-community/push-notification-ios": "^1.10.1", "@react-native-firebase/app": "^15.3.0", "@react-native-firebase/messaging": "^15.3.0", "@react-native-google-signin/google-signin": "^9.0.2", "@react-native-masked-view/masked-view": "^0.2.7", "@react-navigation/drawer": "^6.5.7", "@react-navigation/native": "^6.0.11", "@react-navigation/stack": "^6.2.2", "@shopify/flash-list": "^1.6.3", "add": "^2.0.6", "babel-plugin-transform-remove-console": "^6.9.4", "geolib": "^3.3.4", "i": "^0.3.7", "moment": "^2.29.4", "native-base": "^2.13.14", "npm": "^8.18.0", "react": "17.0.2", "react-apple-login": "^1.1.6", "react-native": "0.68.2", "react-native-audio-recorder-player": "^3.5.1", "react-native-background-fetch": "^2.0.8", "react-native-background-task": "^0.2.1", "react-native-background-timer": "^2.4.1", "react-native-circular-progress-indicator": "^4.4.2", "react-native-confirmation-code-field": "^7.3.2", "react-native-confirmation-code-input": "^1.0.4", "react-native-datepicker": "^1.7.2", "react-native-document-picker": "^8.1.1", "react-native-exit-app": "^2.0.0", "react-native-fast-image": "^8.5.11", "react-native-fbsdk": "^3.0.0", "react-native-geocoding": "^0.5.0", "react-native-geojson": "^0.1.1", "react-native-geolocation-service": "^5.3.0", "react-native-gesture-handler": "^1.10.3", "react-native-google-places-autocomplete": "^2.5.1", "react-native-i18n": "^2.0.15", "react-native-image-picker": "^4.8.4", "react-native-image-resizer": "^1.4.5", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-avoiding-view": "^1.0.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.6.2", "react-native-maps": "^1.1.0", "react-native-maps-directions": "^1.9.0", "react-native-modal-datetime-picker": "^14.0.1", "react-native-modalize": "^2.1.1", "react-native-pager-view": "^5.4.25", "react-native-popover-view": "^5.1.2", "react-native-progress": "^5.0.0", "react-native-push-notification": "^8.1.1", "react-native-raw-bottom-sheet": "^2.2.0", "react-native-reanimated": "^2.9.1", "react-native-render-html": "^6.3.4", "react-native-restart": "^0.0.24", "react-native-safe-area-context": "^4.3.1", "react-native-screens": "^3.15.0", "react-native-select-dropdown": "^2.0.4", "react-native-skeleton-placeholder": "^5.0.0", "react-native-snap-carousel": "^3.9.1", "react-native-sound": "^0.11.2", "react-native-star-rating": "^1.1.0", "react-native-svg": "^13.0.0", "react-native-webview": "^11.22.7", "react-navigation-drawer": "^2.7.2", "react-redux": "^8.0.2", "react-scripts": "^5.0.1", "redux": "^4.2.0", "redux-saga": "^1.1.3", "redux-thunk": "^2.4.1", "rn-fetch-blob": "^0.12.0", "rn-range-slider": "^2.2.2", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2"}, "devDependencies": {"@babel/core": "7.18.9", "@babel/runtime": "7.18.9", "@react-native-community/eslint-config": "2.0.0", "babel-jest": "26.6.3", "eslint": "7.32.0", "eslint-plugin-prettier": "^3.1.1", "jest": "26.6.3", "metro-react-native-babel-preset": "0.67.0", "prettier": "^1.18.2", "react-test-renderer": "17.0.2"}, "jest": {"preset": "react-native"}}